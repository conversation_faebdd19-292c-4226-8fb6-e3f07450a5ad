use tokio_tungstenite::{connect_async, tungstenite::protocol::Message};
use futures_util::{SinkExt, StreamExt};
use serde_json::{json, Value};
use url::Url;
use std::sync::atomic::{AtomicU64, Ordering};
use std::sync::Arc;
use std::collections::{HashMap, VecDeque};
use tokio::sync::mpsc;
use std::time::Duration;

pub mod smoke_test;
pub mod enhanced_transaction_processor;

// 60-second sliding window statistics implementation
#[derive(Debug)]
pub struct WindowStats {
    buys: VecDeque<(std::time::Instant, u64)>,
    sells: VecDeque<(std::time::Instant, u64)>,
}

impl WindowStats {
    pub fn new() -> Self {
        Self {
            buys: VecDeque::new(),
            sells: VecDeque::new(),
        }
    }

    pub fn record_buy(&mut self, amount: u64) {
        let now = std::time::Instant::now();
        self.buys.push_back((now, amount));
        self.cleanup_old_entries();
    }

    pub fn record_sell(&mut self, amount: u64) {
        let now = std::time::Instant::now();
        self.sells.push_back((now, amount));
        self.cleanup_old_entries();
    }

    fn cleanup_old_entries(&mut self) {
        let cutoff = std::time::Instant::now() - std::time::Duration::from_secs(60);
        while let Some(&(timestamp, _)) = self.buys.front() {
            if timestamp < cutoff {
                self.buys.pop_front();
            } else {
                break;
            }
        }
        while let Some(&(timestamp, _)) = self.sells.front() {
            if timestamp < cutoff {
                self.sells.pop_front();
            } else {
                break;
            }
        }
    }

    pub fn totals(&mut self) -> (usize, u64, usize, u64) {
        self.cleanup_old_entries();
        let buy_count = self.buys.len();
        let buy_sum: u64 = self.buys.iter().map(|(_, amount)| amount).sum();
        let sell_count = self.sells.len();
        let sell_sum: u64 = self.sells.iter().map(|(_, amount)| amount).sum();
        (buy_count, buy_sum, sell_count, sell_sum)
    }
}

// Global ID counter for JSON-RPC requests
static REQUEST_ID: AtomicU64 = AtomicU64::new(1);

// Centralized counter management system with enum-based counter types
#[derive(Debug, Clone, Copy)]
pub enum CounterType {
    JitoMsg,
    JitoError,
    HeliusError,
}

pub struct CounterManager {
    counters: [AtomicU64; 3],
}

impl CounterManager {
    pub const fn new() -> Self {
        Self {
            counters: [
                AtomicU64::new(0), // JitoMsg
                AtomicU64::new(0), // JitoError
                AtomicU64::new(0), // HeliusError
            ],
        }
    }

    pub fn increment(&self, counter_type: CounterType) -> u64 {
        self.counters[counter_type as usize].fetch_add(1, Ordering::Relaxed)
    }
}

// Global counter manager instance
static COUNTERS: CounterManager = CounterManager::new();

// Centralized error handling utilities with unified throttled logging
pub mod error_handling {
    use super::{COUNTERS, CounterType};

    // Generic throttled logging function to eliminate code duplication
    fn log_throttled(counter_type: CounterType, message: &str, throttle_interval: u64) {
        let count = COUNTERS.increment(counter_type);
        if count % throttle_interval == 0 {
            println!("{}", message.replace("{}", &count.to_string()));
        }
    }

    pub fn log_json_parse_error_throttled(error: &dyn std::fmt::Display, context: &str, throttle_interval: u64) {
        let message = format!("⚠️  {} JSON parsing error sample #{}: {}", context, "{}", error);
        log_throttled(CounterType::HeliusError, &message, throttle_interval);
    }

    pub fn log_jito_error_throttled(error: &dyn std::fmt::Display, throttle_interval: u64) {
        let message = format!("⚠️  Jito: JSON parsing error sample #{}: {}", "{}", error);
        log_throttled(CounterType::JitoError, &message, throttle_interval);
    }

    pub fn log_jito_message_throttled(message: &str, throttle_interval: u64) {
        let formatted_message = format!("📦 Jito: Received message sample #{}: {}", "{}", message);
        log_throttled(CounterType::JitoMsg, &formatted_message, throttle_interval);
    }


}



// Jito event types
#[derive(Debug, Clone)]
pub enum JitoEvent {
    NewToken { mint: String },
    DevSold { mint: String },
}

// Channel message types for communication between tasks
#[derive(Debug, Clone)]
pub enum SubscriptionCommand {
    Subscribe { mint: String },
    Unsubscribe { mint: String },
}

#[derive(Debug)]
pub struct SubscriptionManager {
    active_subscriptions: HashMap<String, u64>, // mint_address -> subscription_id
    pending_subscriptions: HashMap<u64, String>, // request_id -> mint_address (for tracking subscription confirmations)
}

impl SubscriptionManager {
    pub fn new() -> Self {
        Self {
            active_subscriptions: HashMap::new(),
            pending_subscriptions: HashMap::new(),
        }
    }

    pub fn remove_subscription(&mut self, mint_address: &str) -> Option<u64> {
        self.active_subscriptions.remove(mint_address)
    }

    pub fn add_pending_subscription(&mut self, request_id: u64, mint_address: String) {
        self.pending_subscriptions.insert(request_id, mint_address);
    }

    pub fn complete_pending_subscription(&mut self, request_id: u64, subscription_id: u64) -> Option<String> {
        if let Some(mint_address) = self.pending_subscriptions.remove(&request_id) {
            self.active_subscriptions.insert(mint_address.clone(), subscription_id);
            Some(mint_address)
        } else {
            None
        }
    }

    pub fn is_subscribed(&self, mint_address: &str) -> bool {
        self.active_subscriptions.contains_key(mint_address)
    }

    pub fn get_active_mints(&self) -> Vec<String> {
        self.active_subscriptions.keys().cloned().collect()
    }

    pub fn get_subscription_count(&self) -> usize {
        self.active_subscriptions.len()
    }
}

// Unified JSON-RPC message creation system
pub enum RpcMethod {
    LogsSubscribe { filter: RpcFilter },
    LogsUnsubscribe { subscription_id: u64 },
}

pub enum RpcFilter {
    Mint(String),
    All,
}

pub fn create_rpc_message(method: RpcMethod) -> (Value, u64) {
    let id = REQUEST_ID.fetch_add(1, Ordering::SeqCst);

    let (method_name, params) = match method {
        RpcMethod::LogsSubscribe { filter } => {
            let filter_param = match filter {
                RpcFilter::Mint(mint_address) => json!({"mentions": [mint_address]}),
                RpcFilter::All => json!("all"),
            };
            ("logsSubscribe", json!([filter_param, {"commitment": "confirmed"}]))
        }
        RpcMethod::LogsUnsubscribe { subscription_id } => {
            ("logsUnsubscribe", json!([subscription_id]))
        }
    };

    let message = json!({
        "jsonrpc": "2.0",
        "id": id,
        "method": method_name,
        "params": params
    });

    (message, id)
}

// Convenience functions for backward compatibility
pub fn create_logs_subscribe_message(mint_address: &str) -> (Value, u64) {
    create_rpc_message(RpcMethod::LogsSubscribe {
        filter: RpcFilter::Mint(mint_address.to_string())
    })
}





// Helper function to parse Jito events
pub fn parse_jito_event(json: &Value) -> Option<JitoEvent> {
    let event_type = json.get("type")?.as_str()?;
    let mint = json.get("mint_key")?.as_str()?.to_string();

    match event_type {
        "new_token" => Some(JitoEvent::NewToken { mint }),
        "token_created" => Some(JitoEvent::NewToken { mint }), // Alternative event name
        "token_launch" => Some(JitoEvent::NewToken { mint }), // Alternative event name
        "dev_sell" => Some(JitoEvent::DevSold { mint }),
        "dev_sold" => Some(JitoEvent::DevSold { mint }), // Alternative event name
        _ => None, // Silently ignore unknown event types since the system is working
    }
}

pub fn create_logs_unsubscribe_message(subscription_id: u64) -> Value {
    let (message, _) = create_rpc_message(RpcMethod::LogsUnsubscribe { subscription_id });
    message
}

// Unified WebSocket utilities
pub mod websocket_utils {
    use tokio_tungstenite::tungstenite::protocol::Message;

    // Unified error handling for WebSocket connections
    pub fn handle_websocket_error(error: &tokio_tungstenite::tungstenite::Error, connection_name: &str) {
        println!("❌ {}: WebSocket error: {}", connection_name, error);

        let error_str = error.to_string();
        if error_str.contains("Connection reset") {
            println!("🔄 {}: Connection reset detected - this may be due to network issues", connection_name);
        } else if error_str.contains("Protocol error") {
            println!("🔄 {}: Protocol error detected", connection_name);
        } else if error_str.contains("Io") {
            println!("🔄 {}: I/O error detected - this may be due to network connectivity issues", connection_name);
        }
    }

    // Unified close frame handling
    pub fn handle_close_frame(frame: Option<tokio_tungstenite::tungstenite::protocol::CloseFrame>, connection_name: &str) {
        if let Some(close_frame) = frame {
            println!("🔌 {}: Connection closed - Code: {:?}, Reason: {}",
                    connection_name, close_frame.code, close_frame.reason);
        } else {
            println!("🔌 {}: Connection closed without details", connection_name);
        }
    }

    // Unified message type handling
    pub fn handle_standard_messages(msg: &Message, connection_name: &str) -> bool {
        match msg {
            Message::Binary(data) => {
                println!("📦 {}: Received binary message of {} bytes", connection_name, data.len());
                true
            }
            Message::Ping(data) => {
                println!("🏓 {}: Received ping with {} bytes - responding with pong", connection_name, data.len());
                true
            }
            Message::Pong(data) => {
                println!("🏓 {}: Received pong with {} bytes - connection healthy", connection_name, data.len());
                true
            }
            Message::Frame(_) => {
                // Raw frame, usually not handled at this level
                true
            }
            _ => false, // Let caller handle Text and Close messages
        }
    }
}

// Transaction processing utilities
pub mod transaction_utils {
    use super::*;

    // Unified transaction success detection
    pub fn is_transaction_successful(json: &Value) -> bool {
        // For logsNotification, the err field is directly under /params/result/value/err
        if let Some(err) = json.pointer("/params/result/value/err") {
            // If err exists and is null, transaction is successful
            err.is_null()
        } else {
            // If no err field exists, assume successful (some notifications don't have err field)
            true
        }
    }

    // Enhanced signature extraction with consistent usage
    pub fn extract_signature(json: &Value) -> Option<String> {
        json.pointer("/params/result/value/signature")
            .and_then(|sig| sig.as_str())
            .map(|s| s.to_string())
    }

    // Advanced buy/sell detection with sophisticated pattern matching and context analysis
    pub fn detect_buy_sell_type(logs: &[Value]) -> Option<bool> {
        #[derive(Debug)]
        struct PatternMatch {
            pattern: &'static str,
            weight: i32,
            context_required: Option<&'static str>,
        }

        // High-confidence BUY patterns with weights
        const BUY_PATTERNS: &[PatternMatch] = &[
            // Direct buy indicators (high weight)
            PatternMatch { pattern: "buy", weight: 10, context_required: None },
            PatternMatch { pattern: "Buy", weight: 10, context_required: None },
            PatternMatch { pattern: "BUY", weight: 10, context_required: None },
            PatternMatch { pattern: "purchase", weight: 8, context_required: None },
            PatternMatch { pattern: "Purchase", weight: 8, context_required: None },

            // Swap direction indicators (medium weight)
            PatternMatch { pattern: "swap_base_in", weight: 7, context_required: None },
            PatternMatch { pattern: "swapBaseIn", weight: 7, context_required: None },
            PatternMatch { pattern: "exactIn", weight: 6, context_required: None },
            PatternMatch { pattern: "exact_in", weight: 6, context_required: None },

            // DEX-specific patterns
            PatternMatch { pattern: "Jupiter: Swap", weight: 8, context_required: Some("in") },
            PatternMatch { pattern: "Raydium: Swap", weight: 8, context_required: Some("in") },
            PatternMatch { pattern: "Orca: Swap", weight: 8, context_required: Some("in") },

            // Token transfer patterns (lower weight, need context)
            PatternMatch { pattern: "Transfer", weight: 3, context_required: Some("to") },
            PatternMatch { pattern: "transfer", weight: 3, context_required: Some("to") },

            // Pump.fun specific patterns
            PatternMatch { pattern: "pump.fun: buy", weight: 9, context_required: None },
            PatternMatch { pattern: "Pump: buy", weight: 9, context_required: None },
        ];

        // High-confidence SELL patterns with weights
        const SELL_PATTERNS: &[PatternMatch] = &[
            // Direct sell indicators (high weight)
            PatternMatch { pattern: "sell", weight: 10, context_required: None },
            PatternMatch { pattern: "Sell", weight: 10, context_required: None },
            PatternMatch { pattern: "SELL", weight: 10, context_required: None },
            PatternMatch { pattern: "dispose", weight: 8, context_required: None },
            PatternMatch { pattern: "Dispose", weight: 8, context_required: None },

            // Swap direction indicators (medium weight)
            PatternMatch { pattern: "swap_base_out", weight: 7, context_required: None },
            PatternMatch { pattern: "swapBaseOut", weight: 7, context_required: None },
            PatternMatch { pattern: "exactOut", weight: 6, context_required: None },
            PatternMatch { pattern: "exact_out", weight: 6, context_required: None },

            // DEX-specific patterns
            PatternMatch { pattern: "Jupiter: Swap", weight: 8, context_required: Some("out") },
            PatternMatch { pattern: "Raydium: Swap", weight: 8, context_required: Some("out") },
            PatternMatch { pattern: "Orca: Swap", weight: 8, context_required: Some("out") },

            // Token transfer patterns (lower weight, need context)
            PatternMatch { pattern: "Transfer", weight: 3, context_required: Some("from") },
            PatternMatch { pattern: "transfer", weight: 3, context_required: Some("from") },

            // Pump.fun specific patterns
            PatternMatch { pattern: "pump.fun: sell", weight: 9, context_required: None },
            PatternMatch { pattern: "Pump: sell", weight: 9, context_required: None },
        ];

        let mut buy_score = 0;
        let mut sell_score = 0;
        let mut total_patterns_found = 0;

        for log in logs {
            if let Some(log_str) = log.as_str() {
                let log_lower = log_str.to_lowercase();

                // Check for buy patterns with weighted scoring
                for pattern_match in BUY_PATTERNS {
                    if log_str.contains(pattern_match.pattern) {
                        // Check context requirement if specified
                        let context_valid = if let Some(context) = pattern_match.context_required {
                            log_lower.contains(context)
                        } else {
                            true
                        };

                        if context_valid {
                            buy_score += pattern_match.weight;
                            total_patterns_found += 1;
                        }
                    }
                }

                // Check for sell patterns with weighted scoring
                for pattern_match in SELL_PATTERNS {
                    if log_str.contains(pattern_match.pattern) {
                        // Check context requirement if specified
                        let context_valid = if let Some(context) = pattern_match.context_required {
                            log_lower.contains(context)
                        } else {
                            true
                        };

                        if context_valid {
                            sell_score += pattern_match.weight;
                            total_patterns_found += 1;
                        }
                    }
                }

                // Additional heuristics for better detection

                // Check for SOL -> Token patterns (usually buys)
                if log_str.contains("SOL ->") || log_str.contains("SOL->") {
                    buy_score += 5;
                    total_patterns_found += 1;
                }

                // Check for Token -> SOL patterns (usually sells)
                if log_str.contains("-> SOL") || log_str.contains("->SOL") {
                    sell_score += 5;
                    total_patterns_found += 1;
                }

                // Check for amount patterns that might indicate direction
                if log_str.contains("amount_in") {
                    buy_score += 3;
                } else if log_str.contains("amount_out") {
                    sell_score += 3;
                }
            }
        }

        // Enhanced decision logic with confidence thresholds
        let score_difference = (buy_score - sell_score).abs();
        let min_confidence_threshold = 5; // Minimum score difference for confidence

        // Return result based on weighted scores and confidence
        if total_patterns_found == 0 {
            None // No relevant patterns found
        } else if buy_score > sell_score && score_difference >= min_confidence_threshold {
            Some(true)  // High confidence buy
        } else if sell_score > buy_score && score_difference >= min_confidence_threshold {
            Some(false) // High confidence sell
        } else if buy_score > sell_score {
            Some(true)  // Low confidence buy (still lean towards buy)
        } else if sell_score > buy_score {
            Some(false) // Low confidence sell (still lean towards sell)
        } else {
            None // Tied scores or unclear
        }
    }

    // Extract SOL amount from transaction logs and metadata
    pub fn extract_sol_amount(json: &Value) -> Option<f64> {
        // Try multiple approaches to extract SOL amount

        // 1. Check pre/post balances for SOL changes
        if let Some(sol_amount) = extract_sol_from_balances(json) {
            return Some(sol_amount);
        }

        // 2. Parse logs for amount patterns
        if let Some(logs) = json.pointer("/params/result/value/logs").and_then(|l| l.as_array()) {
            for log in logs {
                if let Some(log_str) = log.as_str() {
                    // Look for common SOL amount patterns in logs
                    if let Some(amount) = parse_sol_from_log_string(log_str) {
                        return Some(amount);
                    }
                }
            }
        }

        None
    }

    // Extract SOL amount from pre/post balance changes
    fn extract_sol_from_balances(json: &Value) -> Option<f64> {
        let pre_balances = json.pointer("/params/result/value/meta/preBalances")?.as_array()?;
        let post_balances = json.pointer("/params/result/value/meta/postBalances")?.as_array()?;
        let account_keys = json.pointer("/params/result/value/transaction/message/accountKeys")?.as_array()?;

        if pre_balances.len() != post_balances.len() || pre_balances.len() != account_keys.len() {
            return None;
        }

        // Look for the user's SOL account (usually the first account that's not a program)
        // and calculate the balance change, excluding fees
        let mut user_balance_changes = Vec::new();

        for i in 0..pre_balances.len() {
            let pre = pre_balances[i].as_u64()?;
            let post = post_balances[i].as_u64()?;
            let account_key = account_keys[i].as_str()?;

            // Skip system programs and known program accounts
            if account_key.starts_with("11111111111111111111111111111111") || // System program
               account_key.starts_with("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA") || // Token program
               account_key.starts_with("9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM") || // Serum DEX
               account_key.starts_with("srmqPvymJeFKQ4zGQed1GFppgkRHL9kaELCbyksJtPX") { // Serum DEX v3
                continue;
            }

            let change = if post > pre {
                (post - pre) as i64
            } else {
                -((pre - post) as i64)
            };

            if change != 0 {
                user_balance_changes.push((i, change, account_key));
            }
        }

        // Find the largest absolute balance change (likely the main SOL transfer)
        if let Some((_, change, _)) = user_balance_changes.iter()
            .max_by_key(|(_, change, _)| change.abs()) {

            let abs_change = change.abs() as u64;
            if abs_change > 5000 { // Minimum threshold of 5000 lamports (0.000005 SOL) to filter out dust
                Some(abs_change as f64 / 1_000_000_000.0) // Convert lamports to SOL
            } else {
                None
            }
        } else {
            None
        }
    }

    // Parse SOL amount from log string patterns using enhanced pattern matching
    fn parse_sol_from_log_string(log_str: &str) -> Option<f64> {
        // Enhanced patterns for different DEX and swap protocols
        let patterns = [
            // Jupiter patterns
            ("Jupiter: Swap ", "lamports"),
            ("Jupiter: ", "SOL"),
            // Raydium patterns
            ("Raydium: Swap ", "lamports"),
            ("Raydium: ", "SOL"),
            // Orca patterns
            ("Orca: Swap ", "lamports"),
            ("Orca: ", "SOL"),
            // Generic patterns
            ("amount:", "auto"),
            ("lamports:", "lamports"),
            ("value:", "auto"),
            ("SOL:", "SOL"),
            ("sol:", "SOL"),
            // Transfer patterns
            ("Transfer ", "lamports"),
            ("transfer ", "lamports"),
            // Pump.fun specific patterns
            ("pump.fun: ", "SOL"),
            ("Pump: ", "SOL"),
        ];

        for (pattern, unit_type) in &patterns {
            if let Some(pos) = log_str.find(pattern) {
                // Extract the part after the pattern
                let after_pattern = &log_str[pos + pattern.len()..];

                // Find the first sequence of digits (with optional decimal point)
                let mut number_str = String::new();
                let mut found_digit = false;

                for ch in after_pattern.chars() {
                    if ch.is_ascii_digit() || (ch == '.' && !number_str.contains('.')) {
                        number_str.push(ch);
                        found_digit = true;
                    } else if found_digit {
                        break; // Stop at first non-digit after finding digits
                    } else if ch.is_whitespace() {
                        continue; // Skip leading whitespace
                    } else {
                        break; // Stop at other characters
                    }
                }

                if let Ok(amount) = number_str.parse::<f64>() {
                    // Convert based on unit type
                    match *unit_type {
                        "lamports" => return Some(amount / 1_000_000_000.0),
                        "SOL" => return Some(amount),
                        "auto" => {
                            // Auto-detect: if number is very large, assume lamports
                            if amount > 1_000_000.0 {
                                return Some(amount / 1_000_000_000.0);
                            } else {
                                return Some(amount);
                            }
                        }
                        _ => return Some(amount),
                    }
                }
            }
        }

        // Fallback: look for any large number that might be lamports
        if let Some(large_number) = extract_large_number_from_log(log_str) {
            if large_number > 1_000_000.0 { // Likely lamports
                return Some(large_number / 1_000_000_000.0);
            }
        }

        None
    }

    // Extract any large number from log that might represent lamports
    fn extract_large_number_from_log(log_str: &str) -> Option<f64> {
        use regex::Regex;

        // Look for numbers with 6+ digits (likely lamports)
        if let Ok(re) = Regex::new(r"\b(\d{6,})\b") {
            if let Some(captures) = re.captures(log_str) {
                if let Some(number_match) = captures.get(1) {
                    if let Ok(number) = number_match.as_str().parse::<f64>() {
                        return Some(number);
                    }
                }
            }
        }

        None
    }

    // Enhanced protocol detection with Pump.fun support
    pub fn detect_protocols(logs: &[Value]) -> Vec<&'static str> {
        const PROTOCOL_PATTERNS: &[(&str, &str)] = &[
            ("6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P", "Pump.fun"), // Pump.fun program ID
            ("pump.fun", "Pump.fun"),
            ("Pump.fun", "Pump.fun"),
            ("Jupiter", "Jupiter"),
            ("JUP", "Jupiter"),
            ("Raydium", "Raydium"),
            ("Orca", "Orca"),
        ];

        let mut detected = Vec::new();
        for log in logs {
            if let Some(log_str) = log.as_str() {
                for (pattern, protocol) in PROTOCOL_PATTERNS {
                    if log_str.contains(pattern) && !detected.contains(protocol) {
                        detected.push(*protocol);
                    }
                }
            }
        }
        detected
    }

    // Extract program interactions efficiently
    pub fn extract_program_interactions(logs: &[Value]) -> Vec<String> {
        logs.iter()
            .filter_map(|log| log.as_str())
            .filter(|log_str| log_str.starts_with("Program ") && log_str.contains(" invoke"))
            .filter_map(|log_str| log_str.split_whitespace().nth(1))
            .map(|s| s.to_string())
            .collect()
    }

    // Enhanced mint validation optimized for logsNotification format
    pub fn transaction_involves_mint(json: &Value, target_mint: &str) -> bool {
        // Validate input parameters
        if target_mint.is_empty() || target_mint.len() != 44 {
            return false; // Invalid mint address format
        }

        // Debug: Log the first few calls to see what we're checking
        use std::sync::atomic::{AtomicU32, Ordering};
        static MINT_CHECK_COUNT: AtomicU32 = AtomicU32::new(0);

        let count = MINT_CHECK_COUNT.fetch_add(1, Ordering::Relaxed);
        if count < 3 {
            println!("🔍 Checking if transaction involves mint: {}", target_mint);
        }

        // CRITICAL FIX: logsNotification doesn't include account keys!
        // We need to rely on subscription filtering instead.
        // Since we're using logsSubscribe with "mentions": [mint_address],
        // Helius should only send us transactions that involve this mint.

        // For logsNotification, if we receive the message, it means the transaction
        // involves our subscribed mint (that's how logsSubscribe works)
        if json.get("method").and_then(|m| m.as_str()) == Some("logsNotification") {
            if count < 3 {
                println!("   ✅ logsNotification received - mint involvement confirmed by subscription filter");
            }
            return true;
        }

        // Fallback: Check account keys (for other message types)
        if let Some(account_keys) = json.pointer("/params/result/value/transaction/message/accountKeys").and_then(|a| a.as_array()) {
            if count < 3 {
                println!("   Account keys found: {}", account_keys.len());
            }
            for key in account_keys {
                if let Some(key_str) = key.as_str() {
                    if key_str == target_mint {
                        if count < 3 {
                            println!("   ✅ Found mint in account keys!");
                        }
                        return true;
                    }
                }
            }
        }

        // 2. Check inner instructions for mint references
        if let Some(inner_instructions) = json.pointer("/params/result/value/meta/innerInstructions").and_then(|i| i.as_array()) {
            for inner_instruction in inner_instructions {
                if let Some(instructions) = inner_instruction.get("instructions").and_then(|i| i.as_array()) {
                    for instruction in instructions {
                        if let Some(accounts) = instruction.get("accounts").and_then(|a| a.as_array()) {
                            for account in accounts {
                                if let Some(account_index) = account.as_u64() {
                                    // Check if this account index corresponds to our target mint
                                    if let Some(account_keys) = json.pointer("/params/result/value/transaction/message/accountKeys").and_then(|a| a.as_array()) {
                                        if let Some(account_key) = account_keys.get(account_index as usize) {
                                            if let Some(key_str) = account_key.as_str() {
                                                if key_str == target_mint {
                                                    return true;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        // 3. Check pre/post token balances for mint references
        if let Some(pre_token_balances) = json.pointer("/params/result/value/meta/preTokenBalances").and_then(|b| b.as_array()) {
            for balance in pre_token_balances {
                if let Some(mint) = balance.get("mint").and_then(|m| m.as_str()) {
                    if mint == target_mint {
                        return true;
                    }
                }
            }
        }

        if let Some(post_token_balances) = json.pointer("/params/result/value/meta/postTokenBalances").and_then(|b| b.as_array()) {
            for balance in post_token_balances {
                if let Some(mint) = balance.get("mint").and_then(|m| m.as_str()) {
                    if mint == target_mint {
                        return true;
                    }
                }
            }
        }

        // 4. Check logs for mint mentions (enhanced pattern matching)
        if let Some(logs) = json.pointer("/params/result/value/logs").and_then(|l| l.as_array()) {
            if count < 3 {
                println!("   Logs found: {}", logs.len());
                if logs.len() > 0 {
                    println!("   Sample log: {:?}", logs.get(0));
                }
            }
            for log in logs {
                if let Some(log_str) = log.as_str() {
                    // Direct mint address match
                    if log_str.contains(target_mint) {
                        if count < 3 {
                            println!("   ✅ Found mint in logs!");
                        }
                        return true;
                    }

                    // Check for common mint-related patterns
                    if log_str.contains("mint:") && log_str.contains(target_mint) {
                        return true;
                    }

                    // Check for token program interactions with this mint
                    if log_str.contains("Token") && log_str.contains(target_mint) {
                        return true;
                    }
                }
            }
        }

        false
    }

    // Additional helper function to validate mint address format
    pub fn is_valid_mint_address(mint: &str) -> bool {
        // Basic validation: Solana addresses are 44 characters long and base58 encoded
        mint.len() == 44 && mint.chars().all(|c| {
            c.is_ascii_alphanumeric() || "123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz".contains(c)
        })
    }
}



// Function removed - now using transaction_utils::extract_signature()



// Function to check if a transaction is Jito-related
#[allow(dead_code)]
fn check_if_jito_transaction(json: &Value) -> bool {
    // Check transaction logs for Jito-related patterns
    if let Some(logs) = json.pointer("/params/result/value/logs").and_then(|l| l.as_array()) {
        for log in logs {
            if let Some(log_str) = log.as_str() {
                // Look for Jito tip program interactions
                if log_str.contains("T1pyyaTNZsKv2WcRAQbZwTo4PntyAjcU8oHInjo6qZV") // Jito tip program
                    || log_str.contains("jito")
                    || log_str.contains("Jito")
                    || log_str.contains("bundle")
                    || log_str.contains("Bundle")
                {
                    return true;
                }
            }
        }
    }

    // Check account keys for Jito tip program
    if let Some(account_keys) = json.pointer("/params/result/value/transaction/message/accountKeys").and_then(|a| a.as_array()) {
        for key in account_keys {
            if let Some(key_str) = key.as_str() {
                if key_str == "T1pyyaTNZsKv2WcRAQbZwTo4PntyAjcU8oHInjo6qZV" {
                    return true;
                }
            }
        }
    }

    false
}

// Function to establish WebSocket connection with retry logic and exponential backoff
#[allow(dead_code)]
async fn connect_with_retry(url: &str, max_attempts: u32) -> Result<tokio_tungstenite::WebSocketStream<tokio_tungstenite::MaybeTlsStream<tokio::net::TcpStream>>, Box<dyn std::error::Error>> {
    let mut attempts = 0;
    let mut delay = Duration::from_secs(1);

    while attempts < max_attempts {
        attempts += 1;

        match connect_async(Url::parse(url)?).await {
            Ok((ws_stream, _)) => {
                println!("✅ WebSocket connection established on attempt {}", attempts);
                return Ok(ws_stream);
            }
            Err(e) => {
                println!("❌ Connection attempt {} failed: {}", attempts, e);
                if attempts < max_attempts {
                    println!("⏳ Retrying in {} seconds...", delay.as_secs());
                    tokio::time::sleep(delay).await;
                    // Exponential backoff with max 30 seconds
                    delay = std::cmp::min(delay * 2, Duration::from_secs(30));
                } else {
                    return Err(format!("Failed to connect after {} attempts: {}", max_attempts, e).into());
                }
            }
        }
    }

    unreachable!()
}

// Function to send periodic ping messages to maintain connection health
#[allow(dead_code)]
async fn send_periodic_ping(
    mut write: futures_util::stream::SplitSink<tokio_tungstenite::WebSocketStream<tokio_tungstenite::MaybeTlsStream<tokio::net::TcpStream>>, Message>,
    mut ping_rx: tokio::sync::mpsc::Receiver<()>
) {
    let mut interval = tokio::time::interval(Duration::from_secs(30));

    loop {
        tokio::select! {
            _ = interval.tick() => {
                let ping_data = b"ping".to_vec();
                if let Err(e) = write.send(Message::Ping(ping_data)).await {
                    println!("❌ Failed to send ping: {}", e);
                    break;
                }
                println!("🏓 Sent ping to maintain connection");
            }
            _ = ping_rx.recv() => {
                println!("🔌 Ping task shutting down");
                break;
            }
        }
    }
}

// Function removed - was unused in the refactored code



// Function to process successful transaction with enhanced metadata extraction
#[allow(dead_code)]
async fn process_successful_transaction(json: &Value) -> Result<(), Box<dyn std::error::Error>> {
    if let Some(signature) = transaction_utils::extract_signature(json) {
        println!("✅ Processing successful transaction: {}", signature);

        // Extract comprehensive transaction metadata
        let mut metadata = HashMap::new();
        metadata.insert("signature".to_string(), signature.clone());

        // Extract slot information
        if let Some(slot) = json.pointer("/params/result/context/slot").and_then(|s| s.as_u64()) {
            metadata.insert("slot".to_string(), slot.to_string());
            println!("   � Slot: {}", slot);
        }

        // Extract block time if available
        if let Some(block_time) = json.pointer("/params/result/value/blockTime").and_then(|t| t.as_u64()) {
            metadata.insert("block_time".to_string(), block_time.to_string());
            let datetime = chrono::DateTime::from_timestamp(block_time as i64, 0)
                .unwrap_or_else(|| chrono::Utc::now());
            println!("   ⏰ Block time: {}", datetime.format("%Y-%m-%d %H:%M:%S UTC"));
        }

        // Extract fee information
        if let Some(fee) = json.pointer("/params/result/value/meta/fee").and_then(|f| f.as_u64()) {
            metadata.insert("fee".to_string(), fee.to_string());
            println!("   💰 Fee: {} lamports", fee);
        }

        // Extract compute units consumed
        if let Some(compute_units) = json.pointer("/params/result/value/meta/computeUnitsConsumed").and_then(|c| c.as_u64()) {
            metadata.insert("compute_units".to_string(), compute_units.to_string());
            println!("   🖥️  Compute units: {}", compute_units);
        }

        // Analyze transaction logs for protocol detection and insights using unified utilities
        if let Some(logs) = json.pointer("/params/result/value/logs").and_then(|l| l.as_array()) {
            println!("   📝 Logs count: {}", logs.len());

            // Use unified protocol detection
            let detected_protocols = transaction_utils::detect_protocols(logs);
            for protocol in &detected_protocols {
                match *protocol {
                    "Jupiter" => println!("   🪐 Jupiter swap detected"),
                    "Raydium" => println!("   🌊 Raydium interaction detected"),
                    "Orca" => println!("   🐋 Orca interaction detected"),
                    _ => {}
                }
            }

            // Use unified program interaction extraction
            let program_interactions = transaction_utils::extract_program_interactions(logs);

            if !detected_protocols.is_empty() {
                metadata.insert("protocols".to_string(), detected_protocols.join(","));
            }
            if !program_interactions.is_empty() {
                metadata.insert("programs".to_string(), program_interactions.join(","));
                println!("   🔧 Program interactions: {}", program_interactions.len());
            }
        }

        // Extract account information
        if let Some(account_keys) = json.pointer("/params/result/value/transaction/message/accountKeys").and_then(|a| a.as_array()) {
            println!("   🔑 Account keys: {}", account_keys.len());
            metadata.insert("account_count".to_string(), account_keys.len().to_string());
        }

        // Extract instruction count
        if let Some(instructions) = json.pointer("/params/result/value/transaction/message/instructions").and_then(|i| i.as_array()) {
            println!("   📋 Instructions: {}", instructions.len());
            metadata.insert("instruction_count".to_string(), instructions.len().to_string());
        }

        // Log metadata summary for debugging
        println!("   📊 Transaction metadata: {:?}", metadata);

    } else {
        println!("⚠️  Successful transaction without signature");
    }

    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio::sync::mpsc;

    #[test]
    fn test_parse_jito_event_new_token() {
        let json = json!({
            "type": "new_token",
            "mint_key": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
        });

        let result = parse_jito_event(&json);
        assert!(result.is_some());

        match result.unwrap() {
            JitoEvent::NewToken { mint } => {
                assert_eq!(mint, "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v");
            }
            _ => panic!("Expected NewToken event"),
        }
    }

    #[test]
    fn test_parse_jito_event_dev_sold() {
        let json = json!({
            "type": "dev_sell",
            "mint_key": "So11111111111111111111111111111111111111112"
        });

        let result = parse_jito_event(&json);
        assert!(result.is_some());

        match result.unwrap() {
            JitoEvent::DevSold { mint } => {
                assert_eq!(mint, "So11111111111111111111111111111111111111112");
            }
            _ => panic!("Expected DevSold event"),
        }
    }

    #[test]
    fn test_parse_jito_event_unknown_type() {
        let json = json!({
            "type": "unknown_event",
            "mint": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
        });

        let result = parse_jito_event(&json);
        assert!(result.is_none());
    }

    #[test]
    fn test_parse_jito_event_missing_fields() {
        // Missing type field
        let json1 = json!({
            "mint_key": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
        });
        assert!(parse_jito_event(&json1).is_none());

        // Missing mint_key field
        let json2 = json!({
            "type": "new_token"
        });
        assert!(parse_jito_event(&json2).is_none());

        // Empty JSON
        let json3 = json!({});
        assert!(parse_jito_event(&json3).is_none());
    }

    #[test]
    fn test_subscription_manager_basic_operations() {
        let mut manager = SubscriptionManager::new();

        // Test initial state
        assert!(!manager.is_subscribed("test_mint"));

        // Test adding pending subscription
        manager.add_pending_subscription(123, "test_mint".to_string());

        // Test completing pending subscription
        let result = manager.complete_pending_subscription(123, 456);
        assert_eq!(result, Some("test_mint".to_string()));
        assert!(manager.is_subscribed("test_mint"));

        // Test removing subscription
        let subscription_id = manager.remove_subscription("test_mint");
        assert_eq!(subscription_id, Some(456));
        assert!(!manager.is_subscribed("test_mint"));
    }

    #[test]
    fn test_subscription_manager_duplicate_prevention() {
        let mut manager = SubscriptionManager::new();

        // Add a subscription
        manager.add_pending_subscription(123, "test_mint".to_string());
        manager.complete_pending_subscription(123, 456);

        // Verify it's tracked as subscribed
        assert!(manager.is_subscribed("test_mint"));

        // Try to complete a non-existent pending subscription
        let result = manager.complete_pending_subscription(999, 789);
        assert_eq!(result, None);

        // Original subscription should still exist
        assert!(manager.is_subscribed("test_mint"));
    }

    #[tokio::test]
    async fn test_channel_communication() {
        let (tx, mut rx) = mpsc::unbounded_channel::<SubscriptionCommand>();

        // Test sending subscribe command
        let mint1 = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v".to_string();
        tx.send(SubscriptionCommand::Subscribe { mint: mint1.clone() }).unwrap();

        // Test sending unsubscribe command
        let mint2 = "So11111111111111111111111111111111111111112".to_string();
        tx.send(SubscriptionCommand::Unsubscribe { mint: mint2.clone() }).unwrap();

        // Verify commands are received correctly
        let cmd1 = rx.recv().await.unwrap();
        match cmd1 {
            SubscriptionCommand::Subscribe { mint } => assert_eq!(mint, mint1),
            _ => panic!("Expected Subscribe command"),
        }

        let cmd2 = rx.recv().await.unwrap();
        match cmd2 {
            SubscriptionCommand::Unsubscribe { mint } => assert_eq!(mint, mint2),
            _ => panic!("Expected Unsubscribe command"),
        }
    }

    #[test]
    fn test_create_logs_subscribe_message() {
        let mint = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v";
        let (message, request_id) = create_logs_subscribe_message(mint);

        // Verify JSON-RPC structure
        assert_eq!(message["jsonrpc"], "2.0");
        assert_eq!(message["method"], "logsSubscribe");
        assert_eq!(message["id"], request_id);

        // Verify parameters
        let params = message["params"].as_array().unwrap();
        assert_eq!(params.len(), 2);

        // Check mentions parameter
        let mentions = &params[0]["mentions"].as_array().unwrap();
        assert_eq!(mentions.len(), 1);
        assert_eq!(mentions[0], mint);

        // Check commitment parameter
        assert_eq!(params[1]["commitment"], "finalized");
    }

    #[test]
    fn test_create_logs_unsubscribe_message() {
        let subscription_id = 12345u64;
        let message = create_logs_unsubscribe_message(subscription_id);

        // Verify JSON-RPC structure
        assert_eq!(message["jsonrpc"], "2.0");
        assert_eq!(message["method"], "logsUnsubscribe");
        assert!(message["id"].is_number());

        // Verify parameters
        let params = message["params"].as_array().unwrap();
        assert_eq!(params.len(), 1);
        assert_eq!(params[0], subscription_id);
    }

    #[test]
    fn test_enhanced_buy_sell_detection() {
        use transaction_utils::detect_buy_sell_type;

        // Test clear buy patterns
        let buy_logs = vec![
            json!("Jupiter: Swap buy 1.5 SOL"),
            json!("Program log: exactIn operation"),
        ];
        assert_eq!(detect_buy_sell_type(&buy_logs), Some(true));

        // Test clear sell patterns
        let sell_logs = vec![
            json!("Raydium: Swap sell 2.0 SOL"),
            json!("Program log: exactOut operation"),
        ];
        assert_eq!(detect_buy_sell_type(&sell_logs), Some(false));

        // Test mixed patterns (should favor higher weighted patterns)
        let mixed_logs = vec![
            json!("pump.fun: buy 0.5 SOL"), // High weight buy
            json!("transfer from account"), // Low weight sell
        ];
        assert_eq!(detect_buy_sell_type(&mixed_logs), Some(true));

        // Test unclear patterns
        let unclear_logs = vec![
            json!("Program log: some operation"),
            json!("Account created"),
        ];
        assert_eq!(detect_buy_sell_type(&unclear_logs), None);

        // Test SOL direction patterns
        let sol_buy_logs = vec![
            json!("Transfer: SOL -> TOKEN"),
        ];
        assert_eq!(detect_buy_sell_type(&sol_buy_logs), Some(true));

        let sol_sell_logs = vec![
            json!("Transfer: TOKEN -> SOL"),
        ];
        assert_eq!(detect_buy_sell_type(&sol_sell_logs), Some(false));
    }

    #[test]
    fn test_mint_validation() {
        use transaction_utils::{transaction_involves_mint, is_valid_mint_address};

        // Test valid mint address format
        let valid_mint = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v";
        assert!(is_valid_mint_address(valid_mint));

        // Test invalid mint address formats
        assert!(!is_valid_mint_address(""));
        assert!(!is_valid_mint_address("short"));
        assert!(!is_valid_mint_address("toolongaddressthatexceeds44characters123456789"));

        // Test transaction with mint in account keys
        let transaction_with_mint = json!({
            "params": {
                "result": {
                    "value": {
                        "transaction": {
                            "message": {
                                "accountKeys": [
                                    "11111111111111111111111111111111",
                                    valid_mint,
                                    "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"
                                ]
                            }
                        },
                        "logs": []
                    }
                }
            }
        });
        assert!(transaction_involves_mint(&transaction_with_mint, valid_mint));

        // Test transaction with mint in logs
        let transaction_with_mint_in_logs = json!({
            "params": {
                "result": {
                    "value": {
                        "transaction": {
                            "message": {
                                "accountKeys": []
                            }
                        },
                        "logs": [
                            format!("Program log: mint: {}", valid_mint)
                        ]
                    }
                }
            }
        });
        assert!(transaction_involves_mint(&transaction_with_mint_in_logs, valid_mint));

        // Test transaction without target mint
        let transaction_without_mint = json!({
            "params": {
                "result": {
                    "value": {
                        "transaction": {
                            "message": {
                                "accountKeys": [
                                    "11111111111111111111111111111111"
                                ]
                            }
                        },
                        "logs": [
                            "Program log: some other operation"
                        ]
                    }
                }
            }
        });
        assert!(!transaction_involves_mint(&transaction_without_mint, valid_mint));
    }
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Load environment variables from .env file
    dotenv::dotenv().ok(); // .ok() makes it non-fatal if .env file doesn't exist

    // Initialize logging
    env_logger::init();

    println!("🚀 Starting Helius Monitoring Bot");
    println!("⏰ Startup time: {}", chrono::Utc::now().format("%Y-%m-%d %H:%M:%S UTC"));

    // Helius WebSocket URL with proper error handling
    let helius_api_key = std::env::var("HELIUS_API_KEY")
        .map_err(|_| {
            "HELIUS_API_KEY not found. Please set it in your .env file or as an environment variable.\n\
             Example .env file content:\n\
             HELIUS_API_KEY=HELIUS_API_KEY"
        })?;

    // Validate API key format (basic check)
    if helius_api_key.len() < 10 || helius_api_key == "HELIUS_API_KEY" || helius_api_key.contains("YOUR_API_KEY") {
        return Err(format!(
            "Invalid HELIUS_API_KEY: '{}'\n\
             Please ensure you have a valid Helius API key from https://dev.helius.xyz/\n\
             The API key should be a long alphanumeric string.",
            helius_api_key
        ).into());
    }

    let url = format!("wss://mainnet.helius-rpc.com/?api-key={}", helius_api_key);

    println!("🔗 Connecting to Helius WebSocket at: wss://mainnet.helius-rpc.com");

    // Connect to the WebSocket server with retry logic
    let ws_stream = connect_with_retry(&url, 3).await?;
    println!("✅ WebSocket handshake completed with Helius at {}", chrono::Utc::now().format("%H:%M:%S"));

    // Split the WebSocket stream into sender and receiver
    let (mut write, mut read) = ws_stream.split();

    // Create subscription manager
    let subscription_manager = Arc::new(tokio::sync::Mutex::new(SubscriptionManager::new()));
    let subscription_manager_clone = subscription_manager.clone();

  

    // No initial subscription - we'll only subscribe to specific mints when Jito events arrive
    println!("🎯 Ready to receive mint-specific subscriptions from Jito ShredStream events...");



    // Spawn task to handle incoming messages (transaction filtering)
    let read_task = tokio::spawn(async move {
        use enhanced_transaction_processor::EnhancedTransactionProcessor;

        let mut processor = EnhancedTransactionProcessor::new(subscription_manager_clone.clone());
        let mut window_map: HashMap<String, WindowStats> = HashMap::new();

        while let Some(msg) = read.next().await {

            match msg {
                Ok(Message::Text(text)) => {
                    // Try to parse as JSON
                    match serde_json::from_str::<Value>(&text) {
                        Ok(json) => {

                            // Handle subscription confirmations and errors (clean output)
                            // Check if this is a subscription confirmation
                            if json.get("result").is_some() && json.get("method").is_none() {
                                if let Some(subscription_id) = json.get("result").and_then(|r| r.as_u64()) {
                                    if let Some(request_id) = json.get("id").and_then(|id| id.as_u64()) {
                                        let mut manager = subscription_manager_clone.lock().await;
                                        if let Some(mint_address) = manager.complete_pending_subscription(request_id, subscription_id) {
                                            println!("✅ Subscribed to mint: {} (ID: {})", mint_address, subscription_id);
                                        }
                                    }
                                } else if let Some(error) = json.get("error") {
                                    println!("❌ Subscription error: {}", error);
                                }
                                continue;
                            }

                            // Check for logsNotification - this is what we get from logsSubscribe
                            if json.get("method").and_then(|m| m.as_str()) == Some("logsNotification") {
                                // Use enhanced transaction processor for mint-specific filtering
                                if let Err(e) = processor.process_logs_notification(&json, &mut window_map).await {
                                    println!("⚠️  Error processing transaction: {}", e);
                                }

                                // All transaction processing is now handled by the enhanced processor
                                // No need for duplicate processing logic here
                            }
                        }
                        Err(e) => {
                            // Use centralized error handling for JSON parsing errors
                            error_handling::log_json_parse_error_throttled(&e, "Helius", 1000);
                        }
                    }
                }
                Ok(msg) => {
                    // Use unified message handling for standard message types
                    if websocket_utils::handle_standard_messages(&msg, "Helius") {
                        continue;
                    }

                    // Handle Close messages specifically
                    if let Message::Close(frame) = msg {
                        websocket_utils::handle_close_frame(frame, "Helius");
                        println!("⚠️  Connection closed by server gracefully");
                        break;
                    }
                }
                Err(e) => {
                    websocket_utils::handle_websocket_error(&e, "Helius");
                    println!("🔄 Connection error, breaking from message loop");
                    break;
                }
            }
        }

        println!("📊 Final Stats - Successful: {}, Total: {}", processor.successful_tx_count, processor.total_tx_count);
    });

    // Create channel for communication between Jito task and Helius write task
    let (jito_tx, mut jito_rx) = mpsc::unbounded_channel::<SubscriptionCommand>();

    // Manual test subscription removed - focusing on Jito ShredStream integration
    println!("🎯 Waiting for Jito ShredStream new_token events to trigger automatic subscriptions...");

    // Connect to Jito ShredStream WebSocket with environment variable support
    let jito_url = std::env::var("JITO_SHREDSTREAM_URL")
        .unwrap_or_else(|_| {
            println!("⚠️  JITO_SHREDSTREAM_URL not set, using default: ws://127.0.0.1:9005");
            "ws://127.0.0.1:9005".to_string()
        });
    println!("Connecting to Jito ShredStream at: {}", jito_url);

    let jito_ws_stream = connect_with_retry(&jito_url, 3).await?;
    println!("✅ WebSocket handshake completed with Jito ShredStream");

    let (jito_write, mut jito_read) = jito_ws_stream.split();

    // Spawn task to handle Jito ShredStream events
    let jito_task = tokio::spawn(async move {
        println!("🎯 Jito ShredStream monitoring task started at {}", chrono::Utc::now().format("%H:%M:%S"));
        let mut new_token_count = 0u64;
        let mut dev_sell_count = 0u64;

        while let Some(msg) = jito_read.next().await {
            match msg {
                Ok(Message::Text(text)) => {
                    match serde_json::from_str::<Value>(&text) {
                        Ok(json) => {

                            if let Some(jito_event) = parse_jito_event(&json) {
                                match jito_event {
                                    JitoEvent::NewToken { mint } => {
                                        new_token_count += 1;
                                        // Silently subscribe to new tokens for clean terminal
                                        if let Err(_) = jito_tx.send(SubscriptionCommand::Subscribe { mint: mint.clone() }) {
                                            // Silently handle subscription errors
                                        }
                                    }
                                    JitoEvent::DevSold { mint } => {
                                        dev_sell_count += 1;
                                        // Silently unsubscribe from dev sold tokens for clean terminal
                                        if let Err(_) = jito_tx.send(SubscriptionCommand::Unsubscribe { mint: mint.clone() }) {
                                            // Silently handle unsubscription errors
                                        }
                                    }
                                }

                                // Compact summary every 25 events to reduce noise
                                if (new_token_count + dev_sell_count) % 25 == 0 {
                                    println!("📊 Jito: {} new tokens, {} dev sells", new_token_count, dev_sell_count);
                                }
                            }
                        }
                        Err(e) => {
                            // Use centralized error handling for Jito JSON parsing errors
                            error_handling::log_jito_error_throttled(&e, 100);
                        }
                    }
                }
                Ok(Message::Close(frame)) => {
                    websocket_utils::handle_close_frame(frame, "Jito");
                    break;
                }
                Err(e) => {
                    websocket_utils::handle_websocket_error(&e, "Jito");
                    break;
                }
                _ => {
                    // Handle other message types if needed
                }
            }
        }

        // Final summary
        println!("🔌 Jito ShredStream connection ended");
        println!("📊 Final Summary:");
        println!("   • New token events: {}", new_token_count);
        println!("   • Dev sell events: {}", dev_sell_count);

        if new_token_count == 0 {
            println!("⚠️  No new_token events received during this session");
        } else {
            println!("✅ Jito ShredStream processed {} new_token events successfully", new_token_count);
        }
    });

    // Spawn task for dynamic subscription management based on Jito events
    let subscription_manager_for_commands = subscription_manager.clone();
    let write_task = tokio::spawn(async move {
        println!("🎯 Subscription management task started at {}", chrono::Utc::now().format("%H:%M:%S"));

        // Keep the write handle alive and process subscription commands
        let mut _jito_write = jito_write; // Keep Jito write handle alive
        let mut subscription_count = 0u64;
        let mut unsubscription_count = 0u64;

        while let Some(command) = jito_rx.recv().await {
            match command {
                SubscriptionCommand::Subscribe { mint } => {
                    subscription_count += 1;
                    // Removed subscription processing logs for clean terminal

                    // Check if already subscribed
                    {
                        let manager = subscription_manager_for_commands.lock().await;
                        if manager.is_subscribed(&mint) {
                            // Skip duplicate subscription silently to reduce noise
                            continue;
                        }
                    }

                    let (subscribe_message, request_id) = create_logs_subscribe_message(&mint);

                    // Removed subscription details for clean terminal

                    // Track the pending subscription
                    {
                        let mut manager = subscription_manager_for_commands.lock().await;
                        manager.add_pending_subscription(request_id, mint.clone());
                    }

                    let message = Message::Text(subscribe_message.to_string());
                    if let Err(_) = write.send(message).await {
                        // Silently handle subscription errors to keep terminal clean
                    } else {
                        // Silently handle successful subscriptions to keep terminal clean
                    }
                }
                SubscriptionCommand::Unsubscribe { mint } => {
                    unsubscription_count += 1;
                    // Removed unsubscription processing logs for clean terminal

                    let subscription_id = {
                        let mut manager = subscription_manager_for_commands.lock().await;
                        manager.remove_subscription(&mint)
                    };

                    if let Some(subscription_id) = subscription_id {
                        let unsubscribe_message = create_logs_unsubscribe_message(subscription_id);

                        // Removed unsubscription details for clean terminal

                        let message = Message::Text(unsubscribe_message.to_string());
                        if let Err(_) = write.send(message).await {
                            // Silently handle unsubscription errors to keep terminal clean
                        } else {
                            // Silently handle successful unsubscriptions to keep terminal clean
                        }
                    } else {
                        // Silently skip if no active subscription found to reduce noise
                        // This can happen if the subscription was already removed
                    }
                }
            }
        }

        println!("🔌 Subscription management task ended at {}", chrono::Utc::now().format("%H:%M:%S"));
        println!("📊 Final subscription stats: {} subscriptions, {} unsubscriptions processed",
               subscription_count, unsubscription_count);
    });

    // Wait for all tasks to complete
    let (read_result, write_result, jito_result) = tokio::join!(read_task, write_task, jito_task);

    if let Err(e) = read_result {
        println!("❌ Helius read task error: {}", e);
    }

    if let Err(e) = write_result {
        println!("❌ Subscription management task error: {}", e);
    }

    if let Err(e) = jito_result {
        println!("❌ Jito task error: {}", e);
    }

    println!("🔌 All WebSocket connections closed");
    Ok(())
}